import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:voodi_v2/main.dart';
import 'package:voodi_v2/screens/interest_calculator_screen.dart';

void main() {
  group('Interest Calculator App Tests', () {
    testWidgets('App loads with Interest Calculator Screen', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());

      // Verify that the app loads with the Interest Calculator Screen
      expect(find.byType(InterestCalculatorScreen), findsOneWidget);
      expect(find.text('Interest Calculator'), findsOneWidget);
    });

    testWidgets('All input fields are present', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());

      // Verify all required input fields are present with new compact labels
      expect(find.text('Principal'), findsOneWidget);
      expect(find.text('From'), findsOneWidget);
      expect(find.text('To'), findsOneWidget);
      expect(find.text('ROI'), findsOneWidget);
      expect(find.text('Type'), findsOneWidget);
      expect(find.text('Calculate Interest'), findsOneWidget);
    });

    testWidgets('Rate selection options are available', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());

      // Verify rate selection options with new simplified labels
      expect(find.text('3%'), findsOneWidget);
      expect(find.text('4%'), findsOneWidget);
      expect(find.text('Other'), findsOneWidget);
    });

    testWidgets('Compound interest toggle works', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());

      // Find and verify compound interest switch with new label
      expect(find.text('Compound'), findsOneWidget);

      // The switch should be on by default
      final switchFinder = find.byType(Switch);
      expect(switchFinder, findsOneWidget);
    });

    testWidgets('Calculate button is present and functional', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());

      // Verify calculate button exists
      expect(find.text('Calculate Interest'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsAtLeastNWidgets(1));

      // Enter principal amount to enable calculation
      await tester.enterText(find.byType(TextField).first, '100000');
      await tester.pump();

      // Verify the input was entered
      expect(find.text('100000'), findsOneWidget);
    });

    testWidgets('Gold rates button is present', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());

      // Verify gold rates button exists in app bar
      expect(find.byIcon(Icons.monetization_on), findsOneWidget);
    });
  });
}
