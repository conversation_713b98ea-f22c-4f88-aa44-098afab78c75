import 'dart:math';

class InterestCalculationResult {
  final double amountToRepay;
  final int totalMonths;
  final int totalDays;
  final double exactMonths;

  InterestCalculationResult({
    required this.amountToRepay,
    required this.totalMonths,
    required this.totalDays,
    required this.exactMonths,
  });
}

class InterestCalculator {
  /// Calculate compound interest with customizable recurring period
  static InterestCalculationResult calculateInterest({
    required double principal,
    required DateTime fromDate,
    required DateTime toDate,
    required double monthlyRate, // Rate per month (e.g., 0.03 for 3%)
    required bool isCompound,
    required int compoundingPeriodMonths, // How often to compound (e.g., 6 for every 6 months)
    required int termAdjustment, // -1, 0, or 1 for term adjustment
  }) {
    // Calculate the exact time difference
    final Duration difference = toDate.difference(fromDate);
    final int totalDays = difference.inDays;
    
    // Calculate months and remaining days
    int totalMonths = 0;
    int remainingDays = totalDays;
    
    DateTime tempDate = fromDate;
    while (tempDate.isBefore(toDate)) {
      final DateTime nextMonth = DateTime(tempDate.year, tempDate.month + 1, tempDate.day);
      if (nextMonth.isBefore(toDate) || nextMonth.isAtSameMomentAs(toDate)) {
        totalMonths++;
        tempDate = nextMonth;
        remainingDays = toDate.difference(tempDate).inDays;
      } else {
        break;
      }
    }
    
    // Calculate exact months (including fractional part)
    double exactMonths = totalMonths + (remainingDays / 30.0);
    
    // Apply term adjustment
    double calculationMonths;
    switch (termAdjustment) {
      case -1: // Round down
        calculationMonths = totalMonths.toDouble();
        break;
      case 1: // Round up
        calculationMonths = (totalMonths + (remainingDays > 0 ? 1 : 0)).toDouble();
        break;
      default: // Exact
        calculationMonths = exactMonths;
        break;
    }
    
    double amountToRepay;
    
    if (!isCompound) {
      // Simple interest calculation
      amountToRepay = principal * (1 + (monthlyRate * calculationMonths));
    } else {
      // Compound interest calculation
      if (compoundingPeriodMonths <= 0) {
        compoundingPeriodMonths = 1; // Default to monthly compounding
      }
      
      // Number of compounding periods
      final double periods = calculationMonths / compoundingPeriodMonths;
      
      // Compound interest formula: A = P(1 + r)^n
      // Where r is the rate per compounding period
      final double ratePerPeriod = monthlyRate * compoundingPeriodMonths;
      amountToRepay = principal * pow(1 + ratePerPeriod, periods);
    }
    
    return InterestCalculationResult(
      amountToRepay: amountToRepay,
      totalMonths: totalMonths,
      totalDays: remainingDays,
      exactMonths: exactMonths,
    );
  }
  
  /// Get default from date (6 months back from today)
  static DateTime getDefaultFromDate() {
    final DateTime now = DateTime.now();
    return DateTime(now.year, now.month - 6, now.day);
  }
  
  /// Get default to date (today)
  static DateTime getDefaultToDate() {
    return DateTime.now();
  }
  
  /// Format currency amount
  static String formatCurrency(double amount) {
    return '₹${amount.toStringAsFixed(2)}';
  }
  
  /// Format term duration
  static String formatTermDuration(int months, int days) {
    if (days == 0) {
      return '$months months';
    } else {
      return '$months months & $days days';
    }
  }
  
  /// Get term adjustment label
  static String getTermAdjustmentLabel(int adjustment, int months, int days) {
    switch (adjustment) {
      case -1:
        return '$months months (~-1 month)';
      case 1:
        return '${months + (days > 0 ? 1 : 0)} months (~+1 month)';
      default:
        return '${(months + (days / 30.0)).toStringAsFixed(1)} months (exact)';
    }
  }
}
