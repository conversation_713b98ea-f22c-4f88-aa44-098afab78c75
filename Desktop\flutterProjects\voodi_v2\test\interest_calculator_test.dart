import 'package:flutter_test/flutter_test.dart';
import 'package:voodi_v2/utils/interest_calculator.dart';

void main() {
  group('Interest Calculator Tests', () {
    test('Simple interest calculation', () {
      final fromDate = DateTime(2024, 1, 1);
      final toDate = DateTime(2024, 7, 1); // 6 months
      
      final result = InterestCalculator.calculateInterest(
        principal: 100000,
        fromDate: fromDate,
        toDate: toDate,
        monthlyRate: 0.03, // 3% per month
        isCompound: false,
        compoundingPeriodMonths: 6,
        termAdjustment: 0, // exact
      );
      
      // 6 months at 3% simple interest: 100000 * (1 + 0.03 * 6) = 118000
      expect(result.amountToRepay, closeTo(118000, 100));
      expect(result.totalMonths, equals(6));
    });

    test('Compound interest calculation - 6 month compounding', () {
      final fromDate = DateTime(2024, 1, 1);
      final toDate = DateTime(2024, 7, 1); // 6 months
      
      final result = InterestCalculator.calculateInterest(
        principal: 100000,
        fromDate: fromDate,
        toDate: toDate,
        monthlyRate: 0.03, // 3% per month
        isCompound: true,
        compoundingPeriodMonths: 6,
        termAdjustment: 0, // exact
      );
      
      // 6 months at 3% per month, compounded every 6 months
      // Rate per period = 0.03 * 6 = 0.18 (18%)
      // Periods = 6/6 = 1
      // Amount = 100000 * (1 + 0.18)^1 = 118000
      expect(result.amountToRepay, closeTo(118000, 100));
    });

    test('Compound interest calculation - 12 months with 6 month compounding', () {
      final fromDate = DateTime(2024, 1, 1);
      final toDate = DateTime(2025, 1, 1); // 12 months
      
      final result = InterestCalculator.calculateInterest(
        principal: 100000,
        fromDate: fromDate,
        toDate: toDate,
        monthlyRate: 0.03, // 3% per month
        isCompound: true,
        compoundingPeriodMonths: 6,
        termAdjustment: 0, // exact
      );
      
      // 12 months at 3% per month, compounded every 6 months
      // Rate per period = 0.03 * 6 = 0.18 (18%)
      // Periods = 12/6 = 2
      // Amount = 100000 * (1 + 0.18)^2 = 100000 * 1.3924 = 139240
      expect(result.amountToRepay, closeTo(139240, 100));
      expect(result.totalMonths, equals(12));
    });

    test('Term adjustment - round up (+1 month)', () {
      final fromDate = DateTime(2024, 1, 1);
      final toDate = DateTime(2024, 6, 15); // 5 months and 15 days
      
      final result = InterestCalculator.calculateInterest(
        principal: 100000,
        fromDate: fromDate,
        toDate: toDate,
        monthlyRate: 0.03,
        isCompound: false,
        compoundingPeriodMonths: 6,
        termAdjustment: 1, // round up
      );
      
      // Should calculate for 6 months (rounded up)
      expect(result.amountToRepay, closeTo(118000, 100));
      expect(result.totalMonths, equals(5)); // Actual months
    });

    test('Term adjustment - round down (-1 month)', () {
      final fromDate = DateTime(2024, 1, 1);
      final toDate = DateTime(2024, 6, 15); // 5 months and 15 days
      
      final result = InterestCalculator.calculateInterest(
        principal: 100000,
        fromDate: fromDate,
        toDate: toDate,
        monthlyRate: 0.03,
        isCompound: false,
        compoundingPeriodMonths: 6,
        termAdjustment: -1, // round down
      );
      
      // Should calculate for 5 months (rounded down)
      expect(result.amountToRepay, closeTo(115000, 100));
      expect(result.totalMonths, equals(5)); // Actual months
    });

    test('Different monthly rates', () {
      final fromDate = DateTime(2024, 1, 1);
      final toDate = DateTime(2024, 7, 1); // 6 months
      
      // Test 4% per month
      final result4Percent = InterestCalculator.calculateInterest(
        principal: 100000,
        fromDate: fromDate,
        toDate: toDate,
        monthlyRate: 0.04, // 4% per month
        isCompound: false,
        compoundingPeriodMonths: 6,
        termAdjustment: 0,
      );
      
      // 6 months at 4% simple interest: 100000 * (1 + 0.04 * 6) = 124000
      expect(result4Percent.amountToRepay, closeTo(124000, 100));
    });

    test('Default dates', () {
      final defaultFromDate = InterestCalculator.getDefaultFromDate();
      final defaultToDate = InterestCalculator.getDefaultToDate();
      
      // From date should be 6 months back
      final now = DateTime.now();
      final expectedFromDate = DateTime(now.year, now.month - 6, now.day);
      
      expect(defaultFromDate.year, equals(expectedFromDate.year));
      expect(defaultFromDate.month, equals(expectedFromDate.month));
      expect(defaultFromDate.day, equals(expectedFromDate.day));
      
      // To date should be today
      expect(defaultToDate.year, equals(now.year));
      expect(defaultToDate.month, equals(now.month));
      expect(defaultToDate.day, equals(now.day));
    });

    test('Currency formatting', () {
      expect(InterestCalculator.formatCurrency(100000), equals('₹100000.00'));
      expect(InterestCalculator.formatCurrency(123456.789), equals('₹123456.79'));
    });

    test('Term duration formatting', () {
      expect(InterestCalculator.formatTermDuration(6, 0), equals('6 months'));
      expect(InterestCalculator.formatTermDuration(5, 15), equals('5 months & 15 days'));
    });

    test('Term adjustment labels', () {
      expect(InterestCalculator.getTermAdjustmentLabel(1, 5, 15), equals('6 months (~+1 month)'));
      expect(InterestCalculator.getTermAdjustmentLabel(-1, 5, 15), equals('5 months (~-1 month)'));
      expect(InterestCalculator.getTermAdjustmentLabel(0, 5, 15), equals('5.5 months (exact)'));
    });

    test('Edge case - same from and to date', () {
      final date = DateTime(2024, 1, 1);
      
      final result = InterestCalculator.calculateInterest(
        principal: 100000,
        fromDate: date,
        toDate: date,
        monthlyRate: 0.03,
        isCompound: false,
        compoundingPeriodMonths: 6,
        termAdjustment: 0,
      );
      
      // No time difference, should return principal amount
      expect(result.amountToRepay, equals(100000));
      expect(result.totalMonths, equals(0));
      expect(result.totalDays, equals(0));
    });

    test('Monthly compounding vs 6-month compounding', () {
      final fromDate = DateTime(2024, 1, 1);
      final toDate = DateTime(2024, 7, 1); // 6 months
      
      // Monthly compounding
      final monthlyResult = InterestCalculator.calculateInterest(
        principal: 100000,
        fromDate: fromDate,
        toDate: toDate,
        monthlyRate: 0.03,
        isCompound: true,
        compoundingPeriodMonths: 1, // monthly
        termAdjustment: 0,
      );
      
      // 6-month compounding
      final sixMonthResult = InterestCalculator.calculateInterest(
        principal: 100000,
        fromDate: fromDate,
        toDate: toDate,
        monthlyRate: 0.03,
        isCompound: true,
        compoundingPeriodMonths: 6, // every 6 months
        termAdjustment: 0,
      );
      
      // Monthly compounding should yield higher amount
      expect(monthlyResult.amountToRepay, greaterThan(sixMonthResult.amountToRepay));
    });
  });
}
