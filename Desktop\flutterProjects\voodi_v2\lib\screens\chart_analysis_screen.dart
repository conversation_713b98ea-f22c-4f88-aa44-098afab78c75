import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../utils/interest_calculator.dart';

class ChartAnalysisScreen extends StatelessWidget {
  final double principal;
  final DateTime fromDate;
  final DateTime toDate;
  final double monthlyRate;
  final bool isCompound;
  final int compoundingPeriodMonths;
  final int termAdjustment;

  const ChartAnalysisScreen({
    super.key,
    required this.principal,
    required this.fromDate,
    required this.toDate,
    required this.monthlyRate,
    required this.isCompound,
    required this.compoundingPeriodMonths,
    required this.termAdjustment,
  });

  @override
  Widget build(BuildContext context) {
    final result = InterestCalculator.calculateInterest(
      principal: principal,
      fromDate: fromDate,
      toDate: toDate,
      monthlyRate: monthlyRate,
      isCompound: isCompound,
      compoundingPeriodMonths: compoundingPeriodMonths,
      termAdjustment: termAdjustment,
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Chart Analysis'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPieChart(context, result),
            const SizedBox(height: 20),
            _buildComparisonChart(context),
            const SizedBox(height: 20),
            _buildGrowthChart(context),
            const SizedBox(height: 20),
            _buildInsights(context, result),
          ],
        ),
      ),
    );
  }

  Widget _buildPieChart(BuildContext context, InterestCalculationResult result) {
    final double totalInterest = result.amountToRepay - principal;
    final double principalPercentage = (principal / result.amountToRepay) * 100;
    final double interestPercentage = (totalInterest / result.amountToRepay) * 100;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.pie_chart, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('Principal vs Interest', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              height: 200,
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.grey.shade300, width: 2),
                      ),
                      child: Stack(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: SweepGradient(
                                startAngle: 0,
                                endAngle: (principalPercentage / 100) * 2 * 3.14159,
                                colors: [
                                  Theme.of(context).colorScheme.primary,
                                  Theme.of(context).colorScheme.primary,
                                ],
                              ),
                            ),
                          ),
                          Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: SweepGradient(
                                startAngle: (principalPercentage / 100) * 2 * 3.14159,
                                endAngle: 2 * 3.14159,
                                colors: [
                                  Theme.of(context).colorScheme.secondary,
                                  Theme.of(context).colorScheme.secondary,
                                ],
                              ),
                            ),
                          ),
                          Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  InterestCalculator.formatCurrency(result.amountToRepay),
                                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                ),
                                const Text('Total', style: TextStyle(fontSize: 12)),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildLegendItem(
                          context,
                          Theme.of(context).colorScheme.primary,
                          'Principal',
                          InterestCalculator.formatCurrency(principal),
                          '${principalPercentage.toStringAsFixed(1)}%',
                        ),
                        const SizedBox(height: 12),
                        _buildLegendItem(
                          context,
                          Theme.of(context).colorScheme.secondary,
                          'Interest',
                          InterestCalculator.formatCurrency(totalInterest),
                          '${interestPercentage.toStringAsFixed(1)}%',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(BuildContext context, Color color, String label, String amount, String percentage) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(label, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
              Text(amount, style: const TextStyle(fontSize: 11)),
              Text(percentage, style: const TextStyle(fontSize: 11, color: Colors.grey)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildComparisonChart(BuildContext context) {
    // Calculate simple vs compound comparison
    final simpleResult = InterestCalculator.calculateInterest(
      principal: principal,
      fromDate: fromDate,
      toDate: toDate,
      monthlyRate: monthlyRate,
      isCompound: false,
      compoundingPeriodMonths: compoundingPeriodMonths,
      termAdjustment: termAdjustment,
    );

    final compoundResult = InterestCalculator.calculateInterest(
      principal: principal,
      fromDate: fromDate,
      toDate: toDate,
      monthlyRate: monthlyRate,
      isCompound: true,
      compoundingPeriodMonths: compoundingPeriodMonths,
      termAdjustment: termAdjustment,
    );

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.compare_arrows, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('Simple vs Compound', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 20),
            _buildComparisonBar(
              context,
              'Simple Interest',
              simpleResult.amountToRepay,
              compoundResult.amountToRepay,
              Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 12),
            _buildComparisonBar(
              context,
              'Compound Interest',
              compoundResult.amountToRepay,
              compoundResult.amountToRepay,
              Theme.of(context).colorScheme.secondary,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.orange.shade700, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Compound interest is ${InterestCalculator.formatCurrency(compoundResult.amountToRepay - simpleResult.amountToRepay)} more than simple interest',
                      style: TextStyle(color: Colors.orange.shade700, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonBar(BuildContext context, String label, double value, double maxValue, Color color) {
    final percentage = value / maxValue;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(label, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
            Text(InterestCalculator.formatCurrency(value), style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          height: 20,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(10),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: percentage,
            child: Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGrowthChart(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('Growth Timeline', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              height: 150,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.show_chart, size: 48, color: Colors.grey.shade400),
                    const SizedBox(height: 8),
                    Text(
                      'Interactive growth chart\nwould be displayed here',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Showing growth from ${InterestCalculator.formatCurrency(principal)} to ${InterestCalculator.formatCurrency(InterestCalculator.calculateInterest(
                        principal: principal,
                        fromDate: fromDate,
                        toDate: toDate,
                        monthlyRate: monthlyRate,
                        isCompound: isCompound,
                        compoundingPeriodMonths: compoundingPeriodMonths,
                        termAdjustment: termAdjustment,
                      ).amountToRepay)}',
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInsights(BuildContext context, InterestCalculationResult result) {
    final double totalInterest = result.amountToRepay - principal;
    final double effectiveAnnualRate = (monthlyRate * 12) * 100;
    
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('Key Insights', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            _buildInsightItem('💰', 'Total interest earned: ${InterestCalculator.formatCurrency(totalInterest)}'),
            _buildInsightItem('📈', 'Effective annual rate: ${effectiveAnnualRate.toStringAsFixed(1)}%'),
            _buildInsightItem('⏱️', 'Investment period: ${result.totalMonths} months ${result.totalDays} days'),
            _buildInsightItem('🔄', isCompound ? 'Compounding every $compoundingPeriodMonths months' : 'Simple interest calculation'),
            _buildInsightItem('📊', 'Interest makes up ${((totalInterest / result.amountToRepay) * 100).toStringAsFixed(1)}% of total amount'),
          ],
        ),
      ),
    );
  }

  Widget _buildInsightItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(text, style: const TextStyle(fontSize: 14)),
          ),
        ],
      ),
    );
  }
}
