import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../utils/interest_calculator.dart';
import 'detailed_results_screen.dart';
import 'chart_analysis_screen.dart';

class InterestCalculatorScreen extends StatefulWidget {
  const InterestCalculatorScreen({super.key});

  @override
  State<InterestCalculatorScreen> createState() => _InterestCalculatorScreenState();
}

class _InterestCalculatorScreenState extends State<InterestCalculatorScreen> {
  // Controllers
  final TextEditingController _principalController = TextEditingController();
  final TextEditingController _fromDateController = TextEditingController();
  final TextEditingController _toDateController = TextEditingController();
  final TextEditingController _customRateController = TextEditingController();
  
  // State variables
  DateTime? _fromDate;
  DateTime? _toDate;
  double _selectedRate = 0.03; // Default 3% per month
  bool _isCustomRate = false;
  bool _isCompoundInterest = true;
  int _compoundingPeriodMonths = 6;
  int _termAdjustment = 1; // Default to +1 month
  InterestCalculationResult? _result;
  
  // Date formatter
  final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');
  
  @override
  void initState() {
    super.initState();
    _initializeDates();
  }
  
  void _initializeDates() {
    _fromDate = InterestCalculator.getDefaultFromDate();
    _toDate = InterestCalculator.getDefaultToDate();
    _fromDateController.text = _dateFormat.format(_fromDate!);
    _toDateController.text = _dateFormat.format(_toDate!);
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Interest Calculator',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.monetization_on),
            onPressed: _showGoldRates,
            tooltip: 'Gold Rates',
          ),
        ],
      ),
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildCompactInputSection(),
            const SizedBox(height: 16),
            _buildCalculateButton(),
            const SizedBox(height: 16),
            if (_result != null) _buildCompactResults(),
            const SizedBox(height: 12),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCompactInputSection() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Principal Amount
            Row(
              children: [
                Icon(Icons.currency_rupee, color: Theme.of(context).colorScheme.primary, size: 18),
                const SizedBox(width: 8),
                const Text('Principal', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: _principalController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    style: const TextStyle(fontSize: 14),
                    decoration: InputDecoration(
                      prefixText: '₹ ',
                      hintText: 'Amount',
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(6)),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      isDense: true,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Dates Row
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('From', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
                      const SizedBox(height: 4),
                      TextField(
                        controller: _fromDateController,
                        style: const TextStyle(fontSize: 12),
                        decoration: InputDecoration(
                          hintText: 'DD/MM/YYYY',
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(6)),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                          isDense: true,
                          suffixIcon: IconButton(
                            icon: Icon(Icons.calendar_today, size: 16, color: Theme.of(context).colorScheme.primary),
                            onPressed: () => _selectFromDate(context),
                            padding: EdgeInsets.zero,
                          ),
                        ),
                        onChanged: _onFromDateTextChanged,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('To', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
                      const SizedBox(height: 4),
                      TextField(
                        controller: _toDateController,
                        style: const TextStyle(fontSize: 12),
                        decoration: InputDecoration(
                          hintText: 'DD/MM/YYYY',
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(6)),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                          isDense: true,
                          suffixIcon: IconButton(
                            icon: Icon(Icons.calendar_today, size: 16, color: Theme.of(context).colorScheme.primary),
                            onPressed: () => _selectToDate(context),
                            padding: EdgeInsets.zero,
                          ),
                        ),
                        onChanged: _onToDateTextChanged,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // ROI Selection
            Row(
              children: [
                Icon(Icons.percent, color: Theme.of(context).colorScheme.primary, size: 18),
                const SizedBox(width: 8),
                const Text('ROI', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
                const SizedBox(width: 16),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: RadioListTile<double>(
                          title: const Text('3%', style: TextStyle(fontSize: 12)),
                          value: 0.03,
                          groupValue: _isCustomRate ? null : _selectedRate,
                          onChanged: (value) => setState(() { _selectedRate = value!; _isCustomRate = false; }),
                          dense: true,
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      Expanded(
                        child: RadioListTile<double>(
                          title: const Text('4%', style: TextStyle(fontSize: 12)),
                          value: 0.04,
                          groupValue: _isCustomRate ? null : _selectedRate,
                          onChanged: (value) => setState(() { _selectedRate = value!; _isCustomRate = false; }),
                          dense: true,
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      Expanded(
                        child: RadioListTile<bool>(
                          title: const Text('Other', style: TextStyle(fontSize: 12)),
                          value: true,
                          groupValue: _isCustomRate,
                          onChanged: (value) => setState(() { _isCustomRate = value!; }),
                          dense: true,
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (_isCustomRate) ...[
              const SizedBox(height: 8),
              TextField(
                controller: _customRateController,
                keyboardType: TextInputType.number,
                style: const TextStyle(fontSize: 12),
                decoration: InputDecoration(
                  suffixText: '% per month',
                  hintText: 'Custom rate',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(6)),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  isDense: true,
                ),
                onChanged: (value) {
                  final rate = double.tryParse(value);
                  if (rate != null) setState(() { _selectedRate = rate / 100; });
                },
              ),
            ],
            const SizedBox(height: 12),
            // Interest Type
            Row(
              children: [
                Icon(Icons.trending_up, color: Theme.of(context).colorScheme.primary, size: 18),
                const SizedBox(width: 8),
                const Text('Type', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
                const SizedBox(width: 16),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: SwitchListTile(
                          title: const Text('Compound', style: TextStyle(fontSize: 12)),
                          value: _isCompoundInterest,
                          onChanged: (value) => setState(() { _isCompoundInterest = value; }),
                          dense: true,
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      if (_isCompoundInterest) ...[
                        const SizedBox(width: 8),
                        Expanded(
                          child: DropdownButtonFormField<int>(
                            value: _compoundingPeriodMonths,
                            style: const TextStyle(fontSize: 12),
                            decoration: InputDecoration(
                              border: OutlineInputBorder(borderRadius: BorderRadius.circular(6)),
                              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              isDense: true,
                            ),
                            items: [1, 3, 6, 12].map((months) {
                              return DropdownMenuItem(
                                value: months,
                                child: Text('${months}m', style: const TextStyle(fontSize: 12)),
                              );
                            }).toList(),
                            onChanged: (value) => setState(() { _compoundingPeriodMonths = value!; }),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCompactResults() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primaryContainer,
              Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.7),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(Icons.calculate, color: Theme.of(context).colorScheme.primary, size: 20),
                  const SizedBox(width: 8),
                  const Text('Results', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                ],
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    _buildCompactResultRow('Term:',
                        InterestCalculator.formatTermDuration(_result!.totalMonths, _result!.totalDays)),
                    const SizedBox(height: 8),
                    _buildCompactResultRow('Amount:',
                        InterestCalculator.formatCurrency(_result!.amountToRepay)),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              // Term Adjustment
              const Text('Term Options:', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: RadioListTile<int>(
                      title: Text('${_result!.totalMonths + (_result!.totalDays > 0 ? 1 : 0)}m', style: const TextStyle(fontSize: 11)),
                      value: 1,
                      groupValue: _termAdjustment,
                      onChanged: (value) => setState(() { _termAdjustment = value!; _calculateInterest(); }),
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  Expanded(
                    child: RadioListTile<int>(
                      title: Text('${_result!.totalMonths}m', style: const TextStyle(fontSize: 11)),
                      value: -1,
                      groupValue: _termAdjustment,
                      onChanged: (value) => setState(() { _termAdjustment = value!; _calculateInterest(); }),
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  Expanded(
                    child: RadioListTile<int>(
                      title: Text('${(_result!.totalMonths + (_result!.totalDays / 30.0)).toStringAsFixed(1)}m', style: const TextStyle(fontSize: 11)),
                      value: 0,
                      groupValue: _termAdjustment,
                      onChanged: (value) => setState(() { _termAdjustment = value!; _calculateInterest(); }),
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _showDetailedView,
                      icon: const Icon(Icons.analytics, size: 16),
                      label: const Text('Detailed View', style: TextStyle(fontSize: 12)),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _showChartAnalysis,
                      icon: const Icon(Icons.pie_chart, size: 16),
                      label: const Text('Charts', style: TextStyle(fontSize: 12)),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildCompactResultRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  void _showDetailedView() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DetailedResultsScreen(
          principal: double.parse(_principalController.text),
          fromDate: _fromDate!,
          toDate: _toDate!,
          monthlyRate: _selectedRate,
          isCompound: _isCompoundInterest,
          compoundingPeriodMonths: _compoundingPeriodMonths,
          termAdjustment: _termAdjustment,
        ),
      ),
    );
  }

  void _showChartAnalysis() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChartAnalysisScreen(
          principal: double.parse(_principalController.text),
          fromDate: _fromDate!,
          toDate: _toDate!,
          monthlyRate: _selectedRate,
          isCompound: _isCompoundInterest,
          compoundingPeriodMonths: _compoundingPeriodMonths,
          termAdjustment: _termAdjustment,
        ),
      ),
    );
  }

  void _showGoldRates() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Gold Rates'),
        content: const Text('Gold rates feature will be implemented soon.\n\nThis will show current gold prices and trends.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
  

  
  Widget _buildCalculateButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _calculateInterest,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: const Text(
          'Calculate Interest',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
  

  
  void _calculateInterest() {
    final principal = double.tryParse(_principalController.text);
    if (principal == null || principal <= 0) {
      _showErrorDialog('Please enter a valid principal amount');
      return;
    }
    
    if (_fromDate == null || _toDate == null) {
      _showErrorDialog('Please select valid dates');
      return;
    }
    
    if (_fromDate!.isAfter(_toDate!)) {
      _showErrorDialog('From date must be before to date');
      return;
    }
    
    setState(() {
      _result = InterestCalculator.calculateInterest(
        principal: principal,
        fromDate: _fromDate!,
        toDate: _toDate!,
        monthlyRate: _selectedRate,
        isCompound: _isCompoundInterest,
        compoundingPeriodMonths: _compoundingPeriodMonths,
        termAdjustment: _termAdjustment,
      );
    });
  }
  
  Future<void> _selectFromDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _fromDate ?? InterestCalculator.getDefaultFromDate(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _fromDate = picked;
        _fromDateController.text = _dateFormat.format(picked);
      });
    }
  }
  
  Future<void> _selectToDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _toDate ?? DateTime.now(),
      firstDate: _fromDate ?? DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      setState(() {
        _toDate = picked;
        _toDateController.text = _dateFormat.format(picked);
      });
    }
  }
  
  void _onFromDateTextChanged(String value) {
    try {
      final date = _dateFormat.parse(value);
      setState(() {
        _fromDate = date;
      });
    } catch (e) {
      // Invalid date format, ignore
    }
  }
  
  void _onToDateTextChanged(String value) {
    try {
      final date = _dateFormat.parse(value);
      setState(() {
        _toDate = date;
      });
    } catch (e) {
      // Invalid date format, ignore
    }
  }
  
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
  
  @override
  void dispose() {
    _principalController.dispose();
    _fromDateController.dispose();
    _toDateController.dispose();
    _customRateController.dispose();
    super.dispose();
  }
}
