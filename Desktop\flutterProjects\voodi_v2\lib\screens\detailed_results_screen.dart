import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:math' as math;
import '../utils/interest_calculator.dart';

class DetailedResultsScreen extends StatelessWidget {
  final double principal;
  final DateTime fromDate;
  final DateTime toDate;
  final double monthlyRate;
  final bool isCompound;
  final int compoundingPeriodMonths;
  final int termAdjustment;

  const DetailedResultsScreen({
    super.key,
    required this.principal,
    required this.fromDate,
    required this.toDate,
    required this.monthlyRate,
    required this.isCompound,
    required this.compoundingPeriodMonths,
    required this.termAdjustment,
  });

  @override
  Widget build(BuildContext context) {
    final result = InterestCalculator.calculateInterest(
      principal: principal,
      fromDate: fromDate,
      toDate: toDate,
      monthlyRate: monthlyRate,
      isCompound: isCompound,
      compoundingPeriodMonths: compoundingPeriodMonths,
      termAdjustment: termAdjustment,
    );

    final breakdown = _generateBreakdown();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Detailed Analysis & Charts'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryAndInsightsCard(context, result),
            const SizedBox(height: 16),
            _buildPieChart(context, result),
            const SizedBox(height: 16),
            _buildComparisonChart(context),
            const SizedBox(height: 16),
            _buildBreakdownCard(context, breakdown),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryAndInsightsCard(BuildContext context, InterestCalculationResult result) {
    final double totalInterest = result.amountToRepay - principal;
    final double effectiveMonthlyRate = monthlyRate * 100;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.summarize, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('Summary & Key Insights', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            _buildSummaryRow('Principal Amount', InterestCalculator.formatCurrency(principal)),
            _buildSummaryRow('Total Interest', InterestCalculator.formatCurrency(totalInterest)),
            _buildSummaryRow('Amount to Repay', InterestCalculator.formatCurrency(result.amountToRepay)),
            _buildSummaryRow('Total Term', '${result.totalMonths} months ${result.totalDays} days'),
            _buildSummaryRow('Interest Rate', '${effectiveMonthlyRate.toStringAsFixed(1)}% per month'),
            _buildSummaryRow('Interest Type', isCompound ? 'Compound (every ${compoundingPeriodMonths}m)' : 'Simple'),

            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),

            // Key Insights Section
            Row(
              children: [
                Icon(Icons.lightbulb, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('Key Insights', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 12),
            _buildInsightItem('💰', 'Total interest earned: ${InterestCalculator.formatCurrency(totalInterest)}'),
            _buildInsightItem('📈', 'Effective monthly rate: ${effectiveMonthlyRate.toStringAsFixed(1)}%'),
            _buildInsightItem('⏱️', 'Investment period: ${result.totalMonths} months ${result.totalDays} days'),
            _buildInsightItem('🔄', isCompound ? 'Compounding every $compoundingPeriodMonths months' : 'Simple interest calculation'),
            _buildInsightItem('📊', 'Interest makes up ${((totalInterest / result.amountToRepay) * 100).toStringAsFixed(1)}% of total amount'),
          ],
        ),
      ),
    );
  }

  Widget _buildPieChart(BuildContext context, InterestCalculationResult result) {
    final double totalInterest = result.amountToRepay - principal;
    final double principalPercentage = (principal / result.amountToRepay) * 100;
    final double interestPercentage = (totalInterest / result.amountToRepay) * 100;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.pie_chart, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('Principal vs Interest', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              height: 200,
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: CustomPaint(
                      painter: PieChartPainter(
                        principalPercentage: principalPercentage,
                        principalColor: Theme.of(context).colorScheme.primary,
                        interestColor: Theme.of(context).colorScheme.secondary,
                      ),
                      child: Container(
                        width: 200,
                        height: 200,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                InterestCalculator.formatCurrency(result.amountToRepay),
                                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                              const Text('Total', style: TextStyle(fontSize: 12)),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildLegendItem(
                          context,
                          Theme.of(context).colorScheme.primary,
                          'Principal',
                          InterestCalculator.formatCurrency(principal),
                          '${principalPercentage.toStringAsFixed(1)}%',
                        ),
                        const SizedBox(height: 12),
                        _buildLegendItem(
                          context,
                          Theme.of(context).colorScheme.secondary,
                          'Interest',
                          InterestCalculator.formatCurrency(totalInterest),
                          '${interestPercentage.toStringAsFixed(1)}%',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonChart(BuildContext context) {
    // Calculate simple vs compound comparison
    final simpleResult = InterestCalculator.calculateInterest(
      principal: principal,
      fromDate: fromDate,
      toDate: toDate,
      monthlyRate: monthlyRate,
      isCompound: false,
      compoundingPeriodMonths: compoundingPeriodMonths,
      termAdjustment: termAdjustment,
    );

    final compoundResult = InterestCalculator.calculateInterest(
      principal: principal,
      fromDate: fromDate,
      toDate: toDate,
      monthlyRate: monthlyRate,
      isCompound: true,
      compoundingPeriodMonths: compoundingPeriodMonths,
      termAdjustment: termAdjustment,
    );

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.compare_arrows, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('Simple vs Compound', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 20),
            _buildComparisonBar(
              context,
              'Simple Interest',
              simpleResult.amountToRepay,
              compoundResult.amountToRepay,
              Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 12),
            _buildComparisonBar(
              context,
              'Compound Interest',
              compoundResult.amountToRepay,
              compoundResult.amountToRepay,
              Theme.of(context).colorScheme.secondary,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.orange.shade700, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Compound interest is ${InterestCalculator.formatCurrency(compoundResult.amountToRepay - simpleResult.amountToRepay)} more than simple interest',
                      style: TextStyle(color: Colors.orange.shade700, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildBreakdownCard(BuildContext context, List<PeriodBreakdown> breakdown) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.timeline, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('Period Breakdown', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            if (breakdown.isEmpty)
              const Text('No breakdown available for this calculation.')
            else
              Column(
                children: breakdown.map((period) => _buildPeriodRow(period)).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 14)),
          Text(value, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildPeriodRow(PeriodBreakdown period) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Period ${period.periodNumber}', style: const TextStyle(fontWeight: FontWeight.bold)),
              Text(period.dateRange, style: const TextStyle(fontSize: 12, color: Colors.grey)),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Opening: ${InterestCalculator.formatCurrency(period.openingBalance)}'),
              Text('Interest: ${InterestCalculator.formatCurrency(period.interestAmount)}'),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Closing: ${InterestCalculator.formatCurrency(period.closingBalance)}', 
                   style: const TextStyle(fontWeight: FontWeight.bold)),
              Text('Rate: ${(period.rate * 100).toStringAsFixed(1)}%'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInsightItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(text, style: const TextStyle(fontSize: 14)),
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(BuildContext context, Color color, String label, String amount, String percentage) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(label, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
              Text(amount, style: const TextStyle(fontSize: 11)),
              Text(percentage, style: const TextStyle(fontSize: 11, color: Colors.grey)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildComparisonBar(BuildContext context, String label, double value, double maxValue, Color color) {
    final percentage = value / maxValue;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(label, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
            Text(InterestCalculator.formatCurrency(value), style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          height: 20,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(10),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: percentage,
            child: Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
        ),
      ],
    );
  }

  List<PeriodBreakdown> _generateBreakdown() {
    if (!isCompound) {
      // For simple interest, show single period
      final totalInterest = principal * monthlyRate * _getCalculationMonths();
      return [
        PeriodBreakdown(
          periodNumber: 1,
          dateRange: '${DateFormat('dd/MM/yy').format(fromDate)} - ${DateFormat('dd/MM/yy').format(toDate)}',
          openingBalance: principal,
          rate: monthlyRate * _getCalculationMonths(),
          interestAmount: totalInterest,
          closingBalance: principal + totalInterest,
        ),
      ];
    }

    // For compound interest, show period-by-period breakdown
    List<PeriodBreakdown> breakdown = [];
    double currentBalance = principal;
    DateTime currentDate = fromDate;
    int periodNumber = 1;

    while (currentDate.isBefore(toDate)) {
      DateTime nextDate = DateTime(currentDate.year, currentDate.month + compoundingPeriodMonths, currentDate.day);
      if (nextDate.isAfter(toDate)) {
        nextDate = toDate;
      }

      final double rateForPeriod = monthlyRate * compoundingPeriodMonths;
      final double interestAmount = currentBalance * rateForPeriod;
      final double closingBalance = currentBalance + interestAmount;

      breakdown.add(PeriodBreakdown(
        periodNumber: periodNumber,
        dateRange: '${DateFormat('dd/MM/yy').format(currentDate)} - ${DateFormat('dd/MM/yy').format(nextDate)}',
        openingBalance: currentBalance,
        rate: rateForPeriod,
        interestAmount: interestAmount,
        closingBalance: closingBalance,
      ));

      currentBalance = closingBalance;
      currentDate = nextDate;
      periodNumber++;

      if (currentDate.isAtSameMomentAs(toDate)) break;
    }

    return breakdown;
  }

  double _getCalculationMonths() {
    final Duration difference = toDate.difference(fromDate);
    final int totalDays = difference.inDays;
    
    int totalMonths = 0;
    int remainingDays = totalDays;
    
    DateTime tempDate = fromDate;
    while (tempDate.isBefore(toDate)) {
      final DateTime nextMonth = DateTime(tempDate.year, tempDate.month + 1, tempDate.day);
      if (nextMonth.isBefore(toDate) || nextMonth.isAtSameMomentAs(toDate)) {
        totalMonths++;
        tempDate = nextMonth;
        remainingDays = toDate.difference(tempDate).inDays;
      } else {
        break;
      }
    }
    
    double exactMonths = totalMonths + (remainingDays / 30.0);
    
    switch (termAdjustment) {
      case -1:
        return totalMonths.toDouble();
      case 1:
        return (totalMonths + (remainingDays > 0 ? 1 : 0)).toDouble();
      default:
        return exactMonths;
    }
  }
}

class PeriodBreakdown {
  final int periodNumber;
  final String dateRange;
  final double openingBalance;
  final double rate;
  final double interestAmount;
  final double closingBalance;

  PeriodBreakdown({
    required this.periodNumber,
    required this.dateRange,
    required this.openingBalance,
    required this.rate,
    required this.interestAmount,
    required this.closingBalance,
  });
}
