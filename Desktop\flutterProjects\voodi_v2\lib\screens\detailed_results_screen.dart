import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../utils/interest_calculator.dart';

class DetailedResultsScreen extends StatelessWidget {
  final double principal;
  final DateTime fromDate;
  final DateTime toDate;
  final double monthlyRate;
  final bool isCompound;
  final int compoundingPeriodMonths;
  final int termAdjustment;

  const DetailedResultsScreen({
    super.key,
    required this.principal,
    required this.fromDate,
    required this.toDate,
    required this.monthlyRate,
    required this.isCompound,
    required this.compoundingPeriodMonths,
    required this.termAdjustment,
  });

  @override
  Widget build(BuildContext context) {
    final result = InterestCalculator.calculateInterest(
      principal: principal,
      fromDate: fromDate,
      toDate: toDate,
      monthlyRate: monthlyRate,
      isCompound: isCompound,
      compoundingPeriodMonths: compoundingPeriodMonths,
      termAdjustment: termAdjustment,
    );

    final breakdown = _generateBreakdown();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Detailed Analysis'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCard(context, result),
            const SizedBox(height: 16),
            _buildParametersCard(context),
            const SizedBox(height: 16),
            _buildBreakdownCard(context, breakdown),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(BuildContext context, InterestCalculationResult result) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.summarize, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('Summary', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            _buildSummaryRow('Principal Amount', InterestCalculator.formatCurrency(principal)),
            _buildSummaryRow('Total Interest', InterestCalculator.formatCurrency(result.amountToRepay - principal)),
            _buildSummaryRow('Amount to Repay', InterestCalculator.formatCurrency(result.amountToRepay)),
            _buildSummaryRow('Total Term', InterestCalculator.formatTermDuration(result.totalMonths, result.totalDays)),
            _buildSummaryRow('Interest Rate', '${(monthlyRate * 100).toStringAsFixed(1)}% per month'),
            _buildSummaryRow('Interest Type', isCompound ? 'Compound (every ${compoundingPeriodMonths}m)' : 'Simple'),
          ],
        ),
      ),
    );
  }

  Widget _buildParametersCard(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('Parameters', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            _buildSummaryRow('From Date', DateFormat('dd/MM/yyyy').format(fromDate)),
            _buildSummaryRow('To Date', DateFormat('dd/MM/yyyy').format(toDate)),
            _buildSummaryRow('Annual Rate', '${(monthlyRate * 12 * 100).toStringAsFixed(1)}%'),
            _buildSummaryRow('Term Adjustment', _getTermAdjustmentText()),
          ],
        ),
      ),
    );
  }

  Widget _buildBreakdownCard(BuildContext context, List<PeriodBreakdown> breakdown) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.timeline, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('Period Breakdown', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            if (breakdown.isEmpty)
              const Text('No breakdown available for this calculation.')
            else
              Column(
                children: breakdown.map((period) => _buildPeriodRow(period)).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 14)),
          Text(value, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildPeriodRow(PeriodBreakdown period) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Period ${period.periodNumber}', style: const TextStyle(fontWeight: FontWeight.bold)),
              Text(period.dateRange, style: const TextStyle(fontSize: 12, color: Colors.grey)),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Opening: ${InterestCalculator.formatCurrency(period.openingBalance)}'),
              Text('Interest: ${InterestCalculator.formatCurrency(period.interestAmount)}'),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Closing: ${InterestCalculator.formatCurrency(period.closingBalance)}', 
                   style: const TextStyle(fontWeight: FontWeight.bold)),
              Text('Rate: ${(period.rate * 100).toStringAsFixed(1)}%'),
            ],
          ),
        ],
      ),
    );
  }

  String _getTermAdjustmentText() {
    switch (termAdjustment) {
      case 1:
        return 'Round Up (+1 month)';
      case -1:
        return 'Round Down (-1 month)';
      default:
        return 'Exact Period';
    }
  }

  List<PeriodBreakdown> _generateBreakdown() {
    if (!isCompound) {
      // For simple interest, show single period
      final totalInterest = principal * monthlyRate * _getCalculationMonths();
      return [
        PeriodBreakdown(
          periodNumber: 1,
          dateRange: '${DateFormat('dd/MM/yy').format(fromDate)} - ${DateFormat('dd/MM/yy').format(toDate)}',
          openingBalance: principal,
          rate: monthlyRate * _getCalculationMonths(),
          interestAmount: totalInterest,
          closingBalance: principal + totalInterest,
        ),
      ];
    }

    // For compound interest, show period-by-period breakdown
    List<PeriodBreakdown> breakdown = [];
    double currentBalance = principal;
    DateTime currentDate = fromDate;
    int periodNumber = 1;

    while (currentDate.isBefore(toDate)) {
      DateTime nextDate = DateTime(currentDate.year, currentDate.month + compoundingPeriodMonths, currentDate.day);
      if (nextDate.isAfter(toDate)) {
        nextDate = toDate;
      }

      final double rateForPeriod = monthlyRate * compoundingPeriodMonths;
      final double interestAmount = currentBalance * rateForPeriod;
      final double closingBalance = currentBalance + interestAmount;

      breakdown.add(PeriodBreakdown(
        periodNumber: periodNumber,
        dateRange: '${DateFormat('dd/MM/yy').format(currentDate)} - ${DateFormat('dd/MM/yy').format(nextDate)}',
        openingBalance: currentBalance,
        rate: rateForPeriod,
        interestAmount: interestAmount,
        closingBalance: closingBalance,
      ));

      currentBalance = closingBalance;
      currentDate = nextDate;
      periodNumber++;

      if (currentDate.isAtSameMomentAs(toDate)) break;
    }

    return breakdown;
  }

  double _getCalculationMonths() {
    final Duration difference = toDate.difference(fromDate);
    final int totalDays = difference.inDays;
    
    int totalMonths = 0;
    int remainingDays = totalDays;
    
    DateTime tempDate = fromDate;
    while (tempDate.isBefore(toDate)) {
      final DateTime nextMonth = DateTime(tempDate.year, tempDate.month + 1, tempDate.day);
      if (nextMonth.isBefore(toDate) || nextMonth.isAtSameMomentAs(toDate)) {
        totalMonths++;
        tempDate = nextMonth;
        remainingDays = toDate.difference(tempDate).inDays;
      } else {
        break;
      }
    }
    
    double exactMonths = totalMonths + (remainingDays / 30.0);
    
    switch (termAdjustment) {
      case -1:
        return totalMonths.toDouble();
      case 1:
        return (totalMonths + (remainingDays > 0 ? 1 : 0)).toDouble();
      default:
        return exactMonths;
    }
  }
}

class PeriodBreakdown {
  final int periodNumber;
  final String dateRange;
  final double openingBalance;
  final double rate;
  final double interestAmount;
  final double closingBalance;

  PeriodBreakdown({
    required this.periodNumber,
    required this.dateRange,
    required this.openingBalance,
    required this.rate,
    required this.interestAmount,
    required this.closingBalance,
  });
}
